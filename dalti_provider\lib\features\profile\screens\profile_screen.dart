import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:file_picker/file_picker.dart';

import '../../../core/widgets/icon_container.dart';
import '../../../core/widgets/main_layout.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../models/profile_models.dart';
import '../providers/profile_provider.dart';
import 'profile_edit_screen.dart';
import 'package:dalti_provider/core/widgets/dalti_avatar.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool _isUploadingLogo = false;
  @override
  void initState() {
    super.initState();
    // Load profile data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).refresh();
    });
  }

  void _onEditProfile() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ProfileEditScreen()));
  }

  Future<void> _onRefresh() async {
    await ref.read(profileProvider.notifier).refresh();
  }

  Future<void> _pickAndUploadLogo() async {
    setState(() {
      _isUploadingLogo = true;
    });

    try {
      final fileResult = await FilePicker.platform.pickFiles(
        type: FileType.image,
        withData: true,
      );

      if (fileResult != null && fileResult.files.isNotEmpty) {
        final file = fileResult.files.first;

        if (file.bytes == null) {
          throw Exception('Could not read file bytes.');
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          throw Exception('File size must be less than 5MB');
        }

        // Validate file type
        final validExtensions = ['png', 'jpg', 'jpeg'];
        final extension = file.extension?.toLowerCase();
        if (extension == null || !validExtensions.contains(extension)) {
          throw Exception('Please select a valid image file (PNG, JPG, JPEG)');
        }

        // Create PlatformFile for upload
        final platformFile = PlatformFile(
          name: file.name,
          size: file.size,
          bytes: file.bytes,
          path: file.path,
        );

        // Upload the logo
        final profileService = ref.read(profileApiServiceProvider);
        final uploadResult = await profileService.uploadBusinessLogo(
          platformFile,
        );

        if (!mounted) return;

        if (uploadResult.success) {
          // Refresh profile data to get the new logo URL
          await ref.read(profileProvider.notifier).refresh();

          if (mounted) {
            // Force a rebuild to ensure the UI updates
            setState(() {});

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  AppLocalizations.of(context).logoUploadedSuccessfully,
                ),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          throw Exception(uploadResult.error ?? 'Upload failed');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload logo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingLogo = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final profileAsync = ref.watch(profileProvider);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: RTLAppBarBuilder.build(
        context: context,
        leading: IconContainer.header(
          icon: Icons.arrow_back,
          onTap: () => context.go('/dashboard'),
        ),
        title: Text(l10n.profile),
        actions: [
          IconContainer.header(icon: Icons.edit, onTap: _onEditProfile),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: profileAsync.when(
          data: (profile) => _buildModernProfileContent(profile),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => _buildErrorWidget(error),
        ),
      ),
    );
  }

  Widget _buildModernProfileContent(ProfileData profile) {
    final l10n = AppLocalizations.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Header Section
          _buildModernSection(
            context: context,
            title: l10n.profileOverview,
            children: [_buildProfileHeaderTile(profile)],
          ),

          const SizedBox(height: 24),

          // Statistics Section
          _buildModernSection(
            context: context,
            title: l10n.statistics,
            children: [_buildStatisticsTile(profile)],
          ),

          const SizedBox(height: 24),

          // Profile Information Section
          _buildModernSection(
            context: context,
            title: l10n.profileInformation,
            children: [
              _buildModernTile(
                context: context,
                icon: Icons.badge,
                title: l10n.title,
                subtitle: profile.title ?? l10n.notSpecified,
                onTap: _onEditProfile,
              ),
              _buildModernTile(
                context: context,
                icon: Icons.phone,
                title: l10n.phone,
                subtitle: profile.phone ?? l10n.notSpecified,
                onTap: _onEditProfile,
              ),
              _buildModernTile(
                context: context,
                icon: Icons.category,
                title: l10n.category,
                subtitle: profile.category?.title ?? l10n.notSpecified,
                onTap: _onEditProfile,
              ),
              _buildModernTile(
                context: context,
                icon: Icons.check_circle,
                title: l10n.setupStatus,
                subtitle: profile.isSetupComplete
                    ? l10n.complete
                    : l10n.incomplete,
                onTap: _onEditProfile,
                subtitleColor: profile.isSetupComplete
                    ? Colors.green
                    : Colors.orange,
                isLast: true,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Business Logo Section
          _buildModernSection(
            context: context,
            title: l10n.businessLogo,
            children: [_buildModernBusinessLogoTile(profile)],
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  // Modern Design Helper Methods
  Widget _buildModernSection({
    required BuildContext context,
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 12),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(
                  context,
                ).colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildModernTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    String? subtitle,
    Color? textColor,
    Color? subtitleColor,
    bool isLast = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: isLast
                ? null
                : Border(
                    bottom: BorderSide(
                      color: Theme.of(
                        context,
                      ).colorScheme.outline.withValues(alpha: 0.2),
                      width: 0.5,
                    ),
                  ),
          ),
          child: Row(
            children: [
              IconContainer.settings(icon: icon),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: textColor,
                      ),
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color:
                              subtitleColor ??
                              Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeaderTile(ProfileData profile) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with avatar and basic info
          Row(
            children: [
              const DaltiAvatar(radius: 30, iconSize: 36),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      profile.title ?? AppLocalizations.of(context).provider,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (profile.category != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        profile.category!.title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (profile.isVerified)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.verified, size: 16, color: Colors.white),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context).verified,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          // About Me section (if presentation exists)
          if (profile.presentation != null &&
              profile.presentation!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        AppLocalizations.of(context).aboutMe,
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    profile.presentation!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatisticsTile(ProfileData profile) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // First row
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.star,
                  title: AppLocalizations.of(context).rating,
                  value: profile.averageRating?.toStringAsFixed(1) ?? '0.0',
                  color: _getStatColor(isDarkMode, 'rating'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.reviews,
                  title: AppLocalizations.of(context).reviews,
                  value: profile.totalReviews.toString(),
                  color: _getStatColor(isDarkMode, 'reviews'),
                ),
              ),
            ],
          ),
          // const SizedBox(height: 16),
          // // Second row
          // Row(
          //   children: [
          //     Expanded(
          //       child: _buildStatCard(
          //         icon: Icons.verified,
          //         title: 'Status',
          //         value: profile.isVerified ? 'Verified' : 'Pending',
          //         color: _getStatColor(isDarkMode, 'status'),
          //       ),
          //     ),
          //     const SizedBox(width: 16),
          //     Expanded(
          //       child: _buildStatCard(
          //         icon: Icons.check_circle,
          //         title: 'Setup',
          //         value: profile.isSetupComplete ? 'Complete' : 'Incomplete',
          //         color: _getStatColor(isDarkMode, 'setup'),
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Get theme-aware colors from dashboard palette for statistics
  Color _getStatColor(bool isDarkMode, String statType) {
    switch (statType) {
      case 'rating':
        // Beach Sand - warm color for rating
        return isDarkMode
            ? const Color(0xFFF4C095) // Original Beach Sand for dark mode
            : const Color(0xFFE8A866); // More saturated for light mode

      case 'reviews':
        // Aqua Wave - cool blue for reviews
        return isDarkMode
            ? const Color(0xFF88C9E8) // Original Aqua Wave for dark mode
            : const Color(0xFF4A9FD9); // More saturated for light mode

      case 'status':
        // Teal Breeze - professional teal for verification status
        return isDarkMode
            ? const Color(0xFF2A9D8F) // Original Teal Breeze
            : const Color(0xFF238A7A); // Slightly darker for light mode

      case 'setup':
        // Stormy Sea - dark professional color for setup status
        return isDarkMode
            ? const Color(
                0xFFF7F5F2,
              ) // Sandy White for dark mode (better contrast)
            : const Color(0xFF264653); // Original Stormy Sea for light mode

      default:
        return const Color(0xFF2A9D8F); // Default to Teal Breeze
    }
  }

  Widget _buildModernBusinessLogoTile(ProfileData profile) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo display area
          Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: profile.logoUrl != null && profile.logoUrl!.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      profile.logoUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildLogoPlaceholder(),
                    ),
                  )
                : _buildLogoPlaceholder(),
          ),

          const SizedBox(height: 16),

          // Upload button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _isUploadingLogo ? null : _pickAndUploadLogo,
              icon: _isUploadingLogo
                  ? const SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Icon(
                      profile.logoUrl != null && profile.logoUrl!.isNotEmpty
                          ? Icons.edit
                          : Icons.upload,
                      size: 18,
                    ),
              label: Text(
                _isUploadingLogo
                    ? 'Uploading...'
                    : (profile.logoUrl != null && profile.logoUrl!.isNotEmpty
                          ? AppLocalizations.of(context).changeLogo
                          : AppLocalizations.of(context).uploadLogo),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                side: BorderSide(color: Theme.of(context).colorScheme.primary),
                foregroundColor: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Format info
          Text(
            AppLocalizations.of(context).supportedFormats,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLogoPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.business,
          size: 32,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context).noLogoUploaded,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).errorLoadingProfile,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => ref.read(profileProvider.notifier).refresh(),
                icon: const Icon(Icons.refresh),
                label: Text(AppLocalizations.of(context).retry),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
