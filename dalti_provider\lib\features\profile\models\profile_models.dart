import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_models.freezed.dart';
part 'profile_models.g.dart';

/// Provider category information
@freezed
class ProfileCategory with _$ProfileCategory {
  const factory ProfileCategory({required int id, required String title}) =
      _ProfileCategory;

  factory ProfileCategory.fromJson(Map<String, dynamic> json) =>
      _$ProfileCategoryFromJson(json);
}

/// Provider profile data
@freezed
class ProfileData with _$ProfileData {
  const ProfileData._();

  const factory ProfileData({
    required int id,
    required String userId,
    String? title,
    String? phone,
    String? presentation,
    required bool isVerified,
    required bool isSetupComplete,
    ProfileCategory? category,
    double? averageRating,
    required int totalReviews,
    String? profilePictureUrl,
    String? logoUrl,
  }) = _ProfileData;

  factory ProfileData.fromJson(Map<String, dynamic> json) =>
      _$ProfileDataFromJson(json);
}

/// Profile update request
@freezed
class ProfileUpdateRequest with _$ProfileUpdateRequest {
  const factory ProfileUpdateRequest({
    String? title,
    String? phone,
    String? presentation,
    int? providerCategoryId,
  }) = _ProfileUpdateRequest;

  factory ProfileUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$ProfileUpdateRequestFromJson(json);
}

/// Profile API response wrapper
@freezed
class ProfileResponse with _$ProfileResponse {
  const factory ProfileResponse({
    required bool success,
    ProfileData? data,
    String? message,
  }) = _ProfileResponse;

  factory ProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$ProfileResponseFromJson(json);
}

/// Profile error information
@freezed
class ProfileError with _$ProfileError {
  const factory ProfileError({
    required String code,
    required String message,
    String? details,
    List<String>? validationErrors,
  }) = _ProfileError;

  factory ProfileError.fromJson(Map<String, dynamic> json) =>
      _$ProfileErrorFromJson(json);
}

/// Profile operation result
@freezed
class ProfileResult with _$ProfileResult {
  const factory ProfileResult.success(ProfileData data) = ProfileSuccess;
  const factory ProfileResult.error(ProfileError error) = ProfileFailure;
}

/// Profile picture upload response data
@freezed
class ProfilePictureUploadResponse with _$ProfilePictureUploadResponse {
  const factory ProfilePictureUploadResponse({
    required String uploadUrl,
    required Map<String, dynamic> uploadFields,
    required UploadFile file,
    required UploadUser user,
  }) = _ProfilePictureUploadResponse;

  factory ProfilePictureUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$ProfilePictureUploadResponseFromJson(json);
}

/// File information from upload response
@freezed
class UploadFile with _$UploadFile {
  const factory UploadFile({
    required String id,
    required String name,
    required String type,
    required String key,
  }) = _UploadFile;

  factory UploadFile.fromJson(Map<String, dynamic> json) =>
      _$UploadFileFromJson(json);
}

/// User information from upload response
@freezed
class UploadUser with _$UploadUser {
  const factory UploadUser({
    required String id,
    required String profilePictureId,
  }) = _UploadUser;

  factory UploadUser.fromJson(Map<String, dynamic> json) =>
      _$UploadUserFromJson(json);
}

/// Profile picture details from the GET endpoint
@freezed
class ProfilePictureDetails with _$ProfilePictureDetails {
  const factory ProfilePictureDetails({
    required String id,
    required String name,
    required String type,
    required String key,
    required String downloadUrl,
    required DateTime createdAt,
  }) = _ProfilePictureDetails;

  factory ProfilePictureDetails.fromJson(Map<String, dynamic> json) =>
      _$ProfilePictureDetailsFromJson(json);
}

/// Wrapper for the profile picture info response
@freezed
class ProfilePictureInfo with _$ProfilePictureInfo {
  const factory ProfilePictureInfo({
    required bool hasProfilePicture,
    ProfilePictureDetails? profilePicture,
  }) = _ProfilePictureInfo;

  factory ProfilePictureInfo.fromJson(Map<String, dynamic> json) =>
      _$ProfilePictureInfoFromJson(json);
}

/// Logo details from the GET endpoint
@freezed
class LogoDetails with _$LogoDetails {
  const factory LogoDetails({
    required String id,
    required String name,
    required String type,
    required String key,
    required String downloadUrl,
    required DateTime createdAt,
  }) = _LogoDetails;

  factory LogoDetails.fromJson(Map<String, dynamic> json) =>
      _$LogoDetailsFromJson(json);
}

/// Wrapper for the logo info response
@freezed
class LogoInfo with _$LogoInfo {
  const factory LogoInfo({required bool hasLogo, LogoDetails? logo}) =
      _LogoInfo;

  factory LogoInfo.fromJson(Map<String, dynamic> json) =>
      _$LogoInfoFromJson(json);
}
