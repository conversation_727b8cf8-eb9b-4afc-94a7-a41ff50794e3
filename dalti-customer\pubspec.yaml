name: dalti_customer
description: "Dalti Customer Mobile App - Comprehensive appointment booking platform for customers"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.9.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Localization
  flutter_localizations:
    sdk: flutter

  # Firebase
  firebase_core: ^4.1.0
  firebase_messaging: ^16.0.1
  firebase_analytics: ^12.0.1
  flutter_local_notifications: ^17.2.4

  # State Management
  riverpod: ^2.6.1
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

  # HTTP Client
  dio: ^5.4.0

  # Navigation
  go_router: ^16.2.1

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1

  # JSON Serialization
  json_annotation: ^4.8.1
  freezed_annotation: ^2.4.4

  # Secure Storage
  flutter_secure_storage: ^9.0.0

  # UI Components
  cupertino_icons: ^1.0.8

  # Utilities
  equatable: ^2.0.5

  # Location Services
  geolocator: ^14.0.2

  # URL Launcher
  url_launcher: ^6.3.1

  # Calendar
  table_calendar: ^3.1.0

  # Internationalization
  intl: ^0.20.2

  # Image Picker
  image_picker: ^1.1.2

  # HTTP for multipart requests
  http: ^1.1.0

  # MIME type detection
  mime: ^2.0.0

  # Path utilities
  path: ^1.9.0

  # Phone number validation
  phone_numbers_parser: ^9.0.11

  # Permission Handler
  permission_handler: ^12.0.1

  # Google Fonts for Changa font
  google_fonts: ^6.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^6.0.0

  # Code Generation
  riverpod_generator: ^2.4.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  freezed: ^2.5.2

  # Testing
  mocktail: ^1.0.0

  # Linting
  custom_lint: ^0.6.4
  riverpod_lint: ^2.3.10

  # App Icon Generator
  flutter_launcher_icons: ^0.14.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Internationalization configuration
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo-app.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/logo-app.png"
    background_color: "#15424E"
    theme_color: "#15424E"
  windows:
    generate: true
    image_path: "assets/images/logo-app.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/logo-app.png"
