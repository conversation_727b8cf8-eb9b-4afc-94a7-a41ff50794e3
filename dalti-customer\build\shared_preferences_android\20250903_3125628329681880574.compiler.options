"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\shared_preferences_android\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\69b0d8716af666fb618491f1d5a65d82\\transformed\\jetified-flutter_embedding_debug-1.0.0-a8bfdfc394deaed5c57bd45a64ac4294dc976a72.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9169e0fdde070432844c830e3cb46266\\transformed\\preference-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\235858557439c70998e7ca975ab3b052\\transformed\\appcompat-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3a9442c8aca3c8a497420716d8413bcf\\transformed\\jetified-fragment-ktx-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f517d10d1a02cef711d9976c4fe1f39\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e4cfb9efcf7502310a635295525a6d5\\transformed\\recyclerview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b59cd59e44a008fbe77fe2977cb5b325\\transformed\\jetified-activity-ktx-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c192a36e3604bb1887c98713d92fa9d2\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74cc7bbb82a69c6035ed907f1dbd91b3\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a1aa99a279fb9e90bbe97ed83937ec36\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e6f2847c3893143d2364abcc9f4f6dc5\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1df4b79ca73c86c93d93c842db8078f8\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7cb4464d7dc570bf9d309cab7aa7cb50\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9f064e415c72cbc81247be09e7509677\\transformed\\jetified-lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\98b77a5aac75f9fc8cecadad261dd91a\\transformed\\jetified-lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c5adc55f009ce7e4eb5ba5b04b715880\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4915f1104689b6508f4c2a92db5bb2ad\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\99fe149b63793fad229668542663d021\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85f579ecd8c14f34a1609f1f9e96e357\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\635ccefa6318656157aeff4353d6f8b9\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f55b8f983dcfa47f85aea6ef814d93b7\\transformed\\jetified-appcompat-resources-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6a1b79887c7709c07723cfd0a61b0902\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e3196ca0d68900b527972f2b3531df60\\transformed\\slidingpanelayout-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5ced5706012751336616d70a8d369e26\\transformed\\coordinatorlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e6d24bdde42cc5c0fddb906a52890cf6\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\98f69a81fbd39500e7e6442cbbf60afb\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fab4b2f4c5e998e97160a889e26c0639\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d4284d2c112873ca7b5d2fc721679865\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\80e48530afe347dec5fdc7a0a580853f\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8593aa4c96124740135359f5661809f7\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f2a98df3a028d8c94d4a0c2625f4452\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\72e373eefa822a94fc409c39eb505a35\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c741a7a1d14c017e329da495182d762a\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8016fc914d31f6e0a0f85feec878cea3\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0114d2762d9f50b59487b353b39eb002\\transformed\\jetified-datastore-preferences-core-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d9f8ded7bd96f1943fe8037caa0fd2e8\\transformed\\jetified-datastore-core-okio-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\43f55c72b4b581675f4bf4c493471757\\transformed\\jetified-datastore-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9639d19dc2226c9f129f14ace0d2bd2b\\transformed\\jetified-datastore-preferences-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\40b73965d74e4693c05cdf51996d6de0\\transformed\\jetified-datastore-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a456766f81789ac0e6b9acbb1069f2cf\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\920e8d843584745883ac96b130bd9a47\\transformed\\jetified-savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ced8e2d90f23613ec9d024b2d7f9eea8\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d80668a302cd685bfd94d19f2787d7cf\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1a408a9b1f2bbe21c8164653ba5b22ec\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\441b27f3c0542b8d109b2b834be52e4b\\transformed\\jetified-collection-ktx-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\019241ad991eedcc25006bb89702cee5\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3b9d154786969411566a0a6df0a28f36\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\482e29a9f2d904d3f1909ed385447ec5\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\327475bd75f5bc1ca0931cd2e4525a42\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\645e0bb7e47e905b46c1c510e32cd6f4\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1b2165736b42842feaa55cff9c8ade7\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00501a4cbb681751356d05a7f9b0c13a\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c84a90b15e69f9d4d4a5a535b3fbea41\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7cd53224325ea47d60c8b732674c302d\\transformed\\jetified-okio-jvm-3.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c88164e3dbcc25468773a04036816039\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9263233919e2cb70f25c1f1e5ef2f14b\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3d21251b0c2edd9772f0bf116446a597\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\37e96f058478d2fa3e964a29915f32ce\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bf0f416bee0299cb5982fc074fdd169d\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\14f9caf53340a661a500cc67a20fa5f1\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd58f369b1f8726088eb9eab24adfdce\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\shared_preferences_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "shared_preferences_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\LegacySharedPreferencesPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\Messages.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesListEncoder.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\MessagesAsync.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.12\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\StringListObjectInputStream.kt"