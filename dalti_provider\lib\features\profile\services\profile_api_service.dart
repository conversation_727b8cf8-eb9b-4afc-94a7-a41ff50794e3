import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:file_picker/file_picker.dart';
import 'package:mime/mime.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../../core/network/api_service.dart';
import '../../../core/network/http_client.dart';
import '../models/profile_models.dart';

/// Result classes for logo upload workflow
class LogoUploadResult {
  final bool success;
  final String? error;
  final String? fileId;
  final String? fileName;
  final String? downloadUrl;

  LogoUploadResult({
    required this.success,
    this.error,
    this.fileId,
    this.fileName,
    this.downloadUrl,
  });

  factory LogoUploadResult.success({
    required String fileId,
    required String fileName,
    String? downloadUrl,
  }) {
    return LogoUploadResult(
      success: true,
      fileId: fileId,
      fileName: fileName,
      downloadUrl: downloadUrl,
    );
  }

  factory LogoUploadResult.error(String error) {
    return LogoUploadResult(success: false, error: error);
  }
}

class InitializeUploadResult {
  final bool success;
  final String? error;
  final String? uploadUrl;
  final Map<String, String>? uploadFields;
  final String? fileId;
  final String? fileName;

  InitializeUploadResult({
    required this.success,
    this.error,
    this.uploadUrl,
    this.uploadFields,
    this.fileId,
    this.fileName,
  });

  factory InitializeUploadResult.success({
    required String uploadUrl,
    required Map<String, String> uploadFields,
    required String fileId,
    required String fileName,
  }) {
    return InitializeUploadResult(
      success: true,
      uploadUrl: uploadUrl,
      uploadFields: uploadFields,
      fileId: fileId,
      fileName: fileName,
    );
  }

  factory InitializeUploadResult.error(String error) {
    return InitializeUploadResult(success: false, error: error);
  }
}

class S3UploadResult {
  final bool success;
  final String? error;

  S3UploadResult({required this.success, this.error});

  factory S3UploadResult.success() {
    return S3UploadResult(success: true);
  }

  factory S3UploadResult.error(String error) {
    return S3UploadResult(success: false, error: error);
  }
}

class ConfirmUploadResult {
  final bool success;
  final String? error;
  final String? downloadUrl;
  final String? fileId;
  final String? fileName;

  ConfirmUploadResult({
    required this.success,
    this.error,
    this.downloadUrl,
    this.fileId,
    this.fileName,
  });

  factory ConfirmUploadResult.success({
    required String fileId,
    required String fileName,
    String? downloadUrl,
  }) {
    return ConfirmUploadResult(
      success: true,
      fileId: fileId,
      fileName: fileName,
      downloadUrl: downloadUrl,
    );
  }

  factory ConfirmUploadResult.error(String error) {
    return ConfirmUploadResult(success: false, error: error);
  }
}

class FileValidationResult {
  final bool isValid;
  final String? error;

  FileValidationResult({required this.isValid, this.error});

  factory FileValidationResult.valid() {
    return FileValidationResult(isValid: true);
  }

  factory FileValidationResult.error(String error) {
    return FileValidationResult(isValid: false, error: error);
  }
}

/// API service for provider profile operations
class ProfileApiService extends ApiService {
  ProfileApiService(HttpClient httpClient) : super(httpClient);

  /// Get provider profile
  Future<ProfileResult> getProfile() async {
    try {
      print('[ProfileApiService] Fetching provider profile');

      final response = await httpClient.get('/api/auth/providers/profile');

      print(
        '[ProfileApiService] Profile response status: ${response.statusCode}',
      );
      print('[ProfileApiService] Profile response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final profileData = ProfileData.fromJson(responseData['data']);
          return ProfileResult.success(profileData);
        } else {
          return ProfileResult.error(
            ProfileError(
              code: 'INVALID_RESPONSE',
              message: responseData['message'] ?? 'Invalid response format',
            ),
          );
        }
      } else {
        return ProfileResult.error(
          ProfileError(
            code: 'HTTP_ERROR',
            message: 'Failed to fetch profile: ${response.statusCode}',
          ),
        );
      }
    } on DioException catch (e) {
      print('[ProfileApiService] Profile fetch error: $e');

      String errorMessage = 'Failed to fetch profile';
      String errorCode = 'NETWORK_ERROR';

      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;

        switch (statusCode) {
          case 401:
            errorCode = 'UNAUTHORIZED';
            errorMessage = 'Authentication required';
            break;
          case 404:
            errorCode = 'NOT_FOUND';
            errorMessage = 'Provider profile not found';
            break;
          case 500:
            errorCode = 'SERVER_ERROR';
            errorMessage = 'Server error occurred';
            break;
          default:
            if (responseData is Map<String, dynamic> &&
                responseData['message'] != null) {
              errorMessage = responseData['message'];
            }
        }
      } else if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        errorCode = 'TIMEOUT';
        errorMessage = 'Request timeout. Please try again.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorCode = 'CONNECTION_ERROR';
        errorMessage =
            'Network connection error. Please check your internet connection.';
      }

      return ProfileResult.error(
        ProfileError(
          code: errorCode,
          message: errorMessage,
          details: e.toString(),
        ),
      );
    } catch (e) {
      print('[ProfileApiService] Unexpected profile fetch error: $e');

      return ProfileResult.error(
        ProfileError(
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Update provider profile
  Future<ProfileResult> updateProfile(ProfileUpdateRequest request) async {
    try {
      print('[ProfileApiService] Updating provider profile');
      print('[ProfileApiService] Update request: ${request.toJson()}');

      final response = await httpClient.put(
        '/api/auth/providers/profile',
        data: request.toJson(),
      );

      print(
        '[ProfileApiService] Update response status: ${response.statusCode}',
      );
      print('[ProfileApiService] Update response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final profileData = ProfileData.fromJson(responseData['data']);
          return ProfileResult.success(profileData);
        } else {
          return ProfileResult.error(
            ProfileError(
              code: 'INVALID_RESPONSE',
              message: responseData['message'] ?? 'Invalid response format',
            ),
          );
        }
      } else {
        return ProfileResult.error(
          ProfileError(
            code: 'HTTP_ERROR',
            message: 'Failed to update profile: ${response.statusCode}',
          ),
        );
      }
    } on DioException catch (e) {
      print('[ProfileApiService] Profile update error: $e');

      String errorMessage = 'Failed to update profile';
      String errorCode = 'NETWORK_ERROR';
      List<String>? validationErrors;

      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;

        switch (statusCode) {
          case 400:
            errorCode = 'VALIDATION_ERROR';
            errorMessage = 'Invalid profile data';
            if (responseData is Map<String, dynamic>) {
              errorMessage = responseData['message'] ?? errorMessage;
              if (responseData['errors'] is List) {
                validationErrors = List<String>.from(responseData['errors']);
              }
            }
            break;
          case 401:
            errorCode = 'UNAUTHORIZED';
            errorMessage = 'Authentication required';
            break;
          case 404:
            errorCode = 'NOT_FOUND';
            errorMessage = 'Provider profile not found';
            break;
          case 500:
            errorCode = 'SERVER_ERROR';
            errorMessage = 'Server error occurred';
            break;
          default:
            if (responseData is Map<String, dynamic> &&
                responseData['message'] != null) {
              errorMessage = responseData['message'];
            }
        }
      } else if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        errorCode = 'TIMEOUT';
        errorMessage = 'Request timeout. Please try again.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorCode = 'CONNECTION_ERROR';
        errorMessage =
            'Network connection error. Please check your internet connection.';
      }

      return ProfileResult.error(
        ProfileError(
          code: errorCode,
          message: errorMessage,
          details: e.toString(),
          validationErrors: validationErrors,
        ),
      );
    } catch (e) {
      print('[ProfileApiService] Unexpected profile update error: $e');

      return ProfileResult.error(
        ProfileError(
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Complete business logo upload workflow
  Future<LogoUploadResult> uploadBusinessLogo(PlatformFile file) async {
    try {
      print('[ProfileApiService] Starting logo upload for: ${file.name}');

      // Validate file
      final validation = _validateLogoFile(file);
      if (!validation.isValid) {
        return LogoUploadResult.error(validation.error!);
      }

      // Step 1: Initialize upload
      final initResult = await _initializeLogoUpload(file);
      if (!initResult.success) {
        return LogoUploadResult.error(
          initResult.error ?? 'Failed to initialize upload',
        );
      }

      // Step 2: Upload to S3
      final s3Result = await _uploadLogoToS3(
        file,
        initResult.uploadUrl!,
        initResult.uploadFields!,
      );
      if (!s3Result.success) {
        return LogoUploadResult.error(
          s3Result.error ?? 'Failed to upload to S3',
        );
      }

      // Step 3: Confirm upload
      final confirmResult = await _confirmLogoUpload(
        initResult.fileId!,
        initResult.fileName!,
      );
      if (!confirmResult.success) {
        return LogoUploadResult.error(
          confirmResult.error ?? 'Failed to confirm upload',
        );
      }

      print(
        '[ProfileApiService] Logo upload successful: ${confirmResult.downloadUrl}',
      );
      return LogoUploadResult.success(
        fileId: confirmResult.fileId!,
        fileName: confirmResult.fileName!,
        downloadUrl: confirmResult.downloadUrl,
      );
    } catch (e) {
      print('[ProfileApiService] Logo upload failed: $e');
      return LogoUploadResult.error(
        'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  /// Validate the logo file before upload
  FileValidationResult _validateLogoFile(PlatformFile file) {
    if (file.bytes == null) {
      return FileValidationResult.error('File data is missing.');
    }
    if (file.size > 5 * 1024 * 1024) {
      return FileValidationResult.error('File size exceeds 5MB limit.');
    }
    final mimeType = lookupMimeType(file.name);
    if (mimeType == null || !['image/png', 'image/jpeg'].contains(mimeType)) {
      return FileValidationResult.error(
        'Invalid file type. Only PNG and JPG are allowed.',
      );
    }
    return FileValidationResult.valid();
  }

  /// Step 1: Initialize the logo upload with the backend
  Future<InitializeUploadResult> _initializeLogoUpload(
    PlatformFile file,
  ) async {
    try {
      final response = await httpClient.post(
        '/api/auth/providers/mobile/logo',
        data: {
          'fileName': file.name,
          'fileType': lookupMimeType(file.name) ?? 'application/octet-stream',
        },
      );

      final data = response.data['data'];
      if (data == null) {
        return InitializeUploadResult.error('Invalid response: missing data');
      }

      final uploadUrl = data['uploadUrl'] as String?;
      final uploadFields = data['uploadFields'] as Map<String, dynamic>?;
      final fileData = data['file'] as Map<String, dynamic>?;

      if (uploadUrl == null || uploadFields == null || fileData == null) {
        return InitializeUploadResult.error(
          'Invalid response: missing required fields',
        );
      }

      final fileId = fileData['id'] as String?;
      final fileName = fileData['name'] as String?;

      if (fileId == null || fileName == null) {
        return InitializeUploadResult.error(
          'Invalid response: missing file information',
        );
      }

      return InitializeUploadResult.success(
        uploadUrl: uploadUrl,
        uploadFields: Map<String, String>.from(uploadFields),
        fileId: fileId,
        fileName: fileName,
      );
    } on DioException catch (e) {
      return InitializeUploadResult.error(
        e.response?.data?['message'] ?? 'Failed to initialize upload',
      );
    }
  }

  /// Step 2: Upload the file to S3
  Future<S3UploadResult> _uploadLogoToS3(
    PlatformFile file,
    String uploadUrl,
    Map<String, String> uploadFields,
  ) async {
    try {
      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));
      request.fields.addAll(uploadFields);
      request.files.add(
        http.MultipartFile.fromBytes(
          'file',
          file.bytes!,
          filename: file.name,
          contentType: MediaType.parse(
            lookupMimeType(file.name) ?? 'application/octet-stream',
          ),
        ),
      );

      final response = await request.send();

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return S3UploadResult.success();
      } else {
        final responseBody = await response.stream.bytesToString();
        return S3UploadResult.error(
          'S3 upload failed with status ${response.statusCode}: $responseBody',
        );
      }
    } catch (e) {
      return S3UploadResult.error('Error during S3 upload: ${e.toString()}');
    }
  }

  /// Step 3: Confirm the logo upload with the backend
  Future<ConfirmUploadResult> _confirmLogoUpload(
    String fileId,
    String fileName,
  ) async {
    try {
      final response = await httpClient.post(
        '/api/auth/providers/mobile/logo/confirm',
        data: {'fileId': fileId, 'fileName': fileName},
      );

      final data = response.data['data'];
      if (data == null) {
        return ConfirmUploadResult.error('Invalid response: missing data');
      }

      final responseFileId = data['id'] as String?;
      final responseFileName = data['name'] as String?;
      final downloadUrl = data['downloadUrl'] as String?;

      if (responseFileId == null || responseFileName == null) {
        return ConfirmUploadResult.error(
          'Invalid response: missing file information',
        );
      }

      return ConfirmUploadResult.success(
        fileId: responseFileId,
        fileName: responseFileName,
        downloadUrl: downloadUrl,
      );
    } on DioException catch (e) {
      return ConfirmUploadResult.error(
        e.response?.data?['message'] ?? 'Failed to confirm upload',
      );
    }
  }

  /// Request a secure URL for profile picture upload
  Future<ProfilePictureUploadResponse> requestProfilePictureUploadUrl(
    String fileName,
    String fileType,
  ) async {
    try {
      print(
        '[ProfileApiService] Requesting profile picture upload URL for $fileName ($fileType)',
      );
      final response = await httpClient.post(
        '/api/auth/user/profile-picture',
        data: {'fileName': fileName, 'fileType': fileType},
      );

      print(
        '[ProfileApiService] Upload URL response status: ${response.statusCode}',
      );
      print('[ProfileApiService] Upload URL response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        if (responseData['success'] == true && responseData['data'] != null) {
          return ProfilePictureUploadResponse.fromJson(responseData['data']);
        } else {
          throw Exception(
            responseData['message'] ?? 'Failed to get upload URL',
          );
        }
      } else {
        throw Exception('Failed to get upload URL: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('[ProfileApiService] Upload URL request error: $e');
      throw Exception('Failed to get upload URL: ${e.message}');
    } catch (e) {
      print('[ProfileApiService] Unexpected error getting upload URL: $e');
      throw Exception('An unexpected error occurred: $e');
    }
  }

  /// Upload profile picture to the secure URL
  Future<void> uploadProfilePicture(
    ProfilePictureUploadResponse uploadInfo,
    List<int> bytes,
    String fileName,
    String fileType,
  ) async {
    try {
      print(
        '[ProfileApiService] Uploading profile picture using multipart POST',
      );
      final request = http.MultipartRequest(
        'POST',
        Uri.parse(uploadInfo.uploadUrl),
      );

      // Add fields from uploadFields
      uploadInfo.uploadFields.forEach((key, value) {
        request.fields[key] = value.toString();
      });

      // Add the file
      request.files.add(
        http.MultipartFile.fromBytes(
          'file',
          bytes,
          filename: fileName,
          contentType: MediaType.parse(fileType),
        ),
      );

      final response = await request.send();

      print(
        '[ProfileApiService] Upload response status: ${response.statusCode}',
      );

      // S3 returns 204 No Content on successful POST upload
      if (response.statusCode != 204) {
        final responseBody = await response.stream.bytesToString();
        print('[ProfileApiService] Upload error response body: $responseBody');
        throw Exception(
          'Failed to upload profile picture: ${response.statusCode}',
        );
      }
    } catch (e) {
      print(
        '[ProfileApiService] Unexpected error uploading profile picture: $e',
      );
      throw Exception('An unexpected error occurred during upload: $e');
    }
  }

  /// Get profile picture information
  Future<ProfilePictureInfo> getProfilePicture() async {
    try {
      print('[ProfileApiService] Fetching profile picture info');

      final response = await httpClient.get('/api/auth/user/profile-picture');

      print(
        '[ProfileApiService] Profile picture info response status: ${response.statusCode}',
      );
      print(
        '[ProfileApiService] Profile picture info response data: ${response.data}',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        if (responseData['success'] == true && responseData['data'] != null) {
          return ProfilePictureInfo.fromJson(responseData['data']);
        } else {
          throw Exception(
            responseData['message'] ?? 'Failed to get profile picture info',
          );
        }
      } else {
        throw Exception(
          'Failed to get profile picture info: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('[ProfileApiService] Profile picture info request error: $e');
      throw Exception('Failed to get profile picture info: ${e.message}');
    } catch (e) {
      print(
        '[ProfileApiService] Unexpected error getting profile picture info: $e',
      );
      throw Exception('An unexpected error occurred: $e');
    }
  }

  /// Get provider categories
  Future<List<ProfileCategory>> getCategories() async {
    try {
      final response = await httpClient.get('/api/auth/providers/categories');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] as List<dynamic>;
        return data.map((json) => ProfileCategory.fromJson(json)).toList();
      }

      throw Exception('Failed to fetch categories: ${response.statusCode}');
    } catch (e) {
      print('[ProfileApiService] Failed to fetch categories: $e');
      throw Exception('Failed to fetch categories: $e');
    }
  }
}
