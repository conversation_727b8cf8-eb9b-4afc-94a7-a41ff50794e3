<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- Allow cleartext traffic for local development -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">***********</domain>
        
        <!-- Add your API domains here -->
        <!-- Example: <domain includeSubdomains="true">api.dalti.com</domain> -->
    </domain-config>
    
    <!-- Default configuration for HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust system certificates -->
            <certificates src="system"/>
            <!-- Trust user-added certificates -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
</network-security-config>
