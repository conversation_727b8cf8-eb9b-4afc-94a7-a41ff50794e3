[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-hdpi_launcher_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-hdpi\\launcher_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-mdpi_launcher_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-mdpi\\launcher_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "org.adscloud.dalti.provider.app-debug-59:/xml_file_paths.xml.flat", "source": "org.adscloud.dalti.provider.app-main-53:/xml/file_paths.xml"}, {"merged": "org.adscloud.dalti.provider.app-debug-59:/xml_network_security_config.xml.flat", "source": "org.adscloud.dalti.provider.app-main-53:/xml/network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-xhdpi_launcher_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-xhdpi\\launcher_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\drawable-v21_launch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\drawable-v21\\launch_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-xxhdpi_launcher_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-xxhdpi\\launcher_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-debug-59:\\mipmap-xxxhdpi_launcher_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\org.adscloud.dalti.provider.app-main-53:\\mipmap-xxxhdpi\\launcher_icon.png"}]