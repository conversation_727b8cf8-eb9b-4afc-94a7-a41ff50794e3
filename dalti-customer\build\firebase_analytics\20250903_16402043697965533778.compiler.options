"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_analytics\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_core\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\69b0d8716af666fb618491f1d5a65d82\\transformed\\jetified-flutter_embedding_debug-1.0.0-a8bfdfc394deaed5c57bd45a64ac4294dc976a72.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\06ba8a5621cf12ac3b89085c888ca82d\\transformed\\jetified-firebase-analytics-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\34c1d53c14e90524277d85a88b9d864c\\transformed\\jetified-play-services-measurement-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\826872335bff3a60830cb82bb0206bc4\\transformed\\jetified-play-services-measurement-api-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a1643167fd3a5c86ede3db7ff225efa6\\transformed\\jetified-play-services-measurement-sdk-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e2c8426ed75c3e3ff075360573bab2d2\\transformed\\jetified-play-services-measurement-impl-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df1bbeccf52bcef1b7bbd4f8da343fb7\\transformed\\jetified-play-services-ads-identifier-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5b937045781c0556436a73ba3bee922d\\transformed\\jetified-play-services-measurement-sdk-api-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ff23435872f4d45f8cc206cb32c8cf6e\\transformed\\jetified-play-services-measurement-base-23.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e185b4054632d048671ee30380cada5a\\transformed\\jetified-play-services-stats-17.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08e195c246adda850666edce0eb0ae61\\transformed\\jetified-firebase-installations-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\08070621314b4a63ca19c1e8b0520983\\transformed\\jetified-firebase-installations-interop-17.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85d5a1428da699bbd013d627c9e28aa0\\transformed\\jetified-play-services-base-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\38ed23ad8cf1e6c238590e465d0cc882\\transformed\\jetified-firebase-common-22.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c192a36e3604bb1887c98713d92fa9d2\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a1aa99a279fb9e90bbe97ed83937ec36\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e6f2847c3893143d2364abcc9f4f6dc5\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7cb4464d7dc570bf9d309cab7aa7cb50\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1df4b79ca73c86c93d93c842db8078f8\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c5adc55f009ce7e4eb5ba5b04b715880\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4915f1104689b6508f4c2a92db5bb2ad\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\99fe149b63793fad229668542663d021\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8016fc914d31f6e0a0f85feec878cea3\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c741a7a1d14c017e329da495182d762a\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1e735157f38d0d2a8151286fd5206ce\\transformed\\jetified-kotlinx-coroutines-android-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7176a2277a2320cd6f5340e23cfc156e\\transformed\\jetified-ads-adservices-java-1.1.0-beta11-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c38e3e09e9dedf508283f301148338e2\\transformed\\jetified-ads-adservices-1.1.0-beta11-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0f97730e29ee53eb56cecc56e7cd0563\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f3561febae5340ea5ca68e9a76a500c4\\transformed\\jetified-kotlinx-coroutines-play-services-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\49994acf6d8962f4b60484cb4dc7bef8\\transformed\\jetified-play-services-tasks-18.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\45ed39d7842061b183dd59a3208532b9\\transformed\\jetified-firebase-measurement-connector-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4d4d650cb543436a61541780a2f1c250\\transformed\\jetified-play-services-basement-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f517d10d1a02cef711d9976c4fe1f39\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85f579ecd8c14f34a1609f1f9e96e357\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\635ccefa6318656157aeff4353d6f8b9\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1e378c1ec38a384cf7278ec5afecebc0\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8593aa4c96124740135359f5661809f7\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f2a98df3a028d8c94d4a0c2625f4452\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\72e373eefa822a94fc409c39eb505a35\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d80668a302cd685bfd94d19f2787d7cf\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1a408a9b1f2bbe21c8164653ba5b22ec\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ced8e2d90f23613ec9d024b2d7f9eea8\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\482e29a9f2d904d3f1909ed385447ec5\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\327475bd75f5bc1ca0931cd2e4525a42\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\645e0bb7e47e905b46c1c510e32cd6f4\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\65d0a160365ba015ce7dde37094f374a\\transformed\\jetified-annotation-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a456766f81789ac0e6b9acbb1069f2cf\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3d21251b0c2edd9772f0bf116446a597\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\37e96f058478d2fa3e964a29915f32ce\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\03091b91f9d4050534640105844735c8\\transformed\\jetified-guava-31.1-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b2de79ba10d79460c592fdf614f3bc82\\transformed\\jetified-listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bf0f416bee0299cb5982fc074fdd169d\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\14f9caf53340a661a500cc67a20fa5f1\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd58f369b1f8726088eb9eab24adfdce\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e952bd0dcc29114fc84cbc1c4ad74b28\\transformed\\jetified-failureaccess-1.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a95ce1d0823b8255ee68a8a1e5912457\\transformed\\jetified-jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4457203b461a2c310a4dbe503a24446d\\transformed\\jetified-checker-qual-3.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4234f29a57b478c3c18caafd3296cefe\\transformed\\jetified-error_prone_annotations-2.26.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aecaf7c8da4d87fc4df88cf6cbd5a658\\transformed\\jetified-j2objc-annotations-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\64071eaf6e17d83cdafbcc36e8d613ee\\transformed\\jetified-firebase-components-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4989699491fdb084b1768623cda1ecb2\\transformed\\jetified-firebase-annotations-17.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_analytics\\tmp\\kotlin-classes\\debug" "-jvm-target" "17" "-module-name" "firebase_analytics_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\build\\firebase_analytics\\generated\\source\\buildConfig\\debug\\io\\flutter\\plugins\\firebase\\analytics\\BuildConfig.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\Constants.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\FlutterFirebaseAnalyticsPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\FlutterFirebaseAppRegistrar.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-12.0.1\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\GeneratedAndroidFirebaseAnalytics.g.kt"