{"logs": [{"outputFile": "org.adscloud.dalti.dalti_customer.app-mergeDebugResources-50:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a2bb78523596e649afe63bece9c973ac\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "126,133,139,274,275,276,277,376,1974,1976,1977,1982,1984", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6578,7084,7487,16765,16818,16871,16924,22394,127612,127788,127910,128172,128367", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6662,7150,7555,16813,16866,16919,16972,22449,127673,127905,127966,128233,128429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d4d69cd4063662f4870b4bb3bcda6c2b\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "371", "startColumns": "4", "startOffsets": "22100", "endColumns": "53", "endOffsets": "22149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df104370204d6f73bddc8d757655b422\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "326,342,373", "startColumns": "4,4,4", "startOffsets": "19818,20572,22204", "endColumns": "56,64,63", "endOffsets": "19870,20632,22263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744a14c4162cc1cc008e947e42d41a58\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "378,425", "startColumns": "4,4", "startOffsets": "22511,26275", "endColumns": "67,166", "endOffsets": "22574,26437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3d0dabce7e4774ae1b88c68846e72921\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "85,86,87,88,228,229,436,438,439,440", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3874,3932,3998,4061,13553,13624,27606,27731,27798,27877", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3927,3993,4056,4118,13619,13691,27669,27793,27872,27941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\210bb542128be41e9375e7e70847760f\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,69,70,74,75,77,78,79,80,81,82,83,84,89,90,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,129,130,131,132,134,135,136,137,138,140,141,142,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,237,238,242,243,244,245,246,247,248,278,279,280,281,282,283,284,285,321,322,323,324,329,337,338,343,367,374,375,377,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,446,451,452,453,454,455,456,464,465,469,473,477,482,488,495,499,503,508,512,516,520,524,528,532,538,542,548,552,558,562,567,571,574,578,584,588,594,598,604,607,611,615,619,623,627,628,629,630,633,636,639,642,646,647,648,649,650,653,655,657,659,664,665,669,675,679,680,682,693,694,698,704,708,709,710,714,741,745,746,750,778,948,974,1145,1171,1202,1210,1216,1230,1252,1257,1262,1272,1281,1290,1294,1301,1309,1316,1317,1326,1329,1332,1336,1340,1344,1347,1348,1353,1358,1368,1373,1380,1386,1387,1390,1394,1399,1401,1403,1406,1409,1411,1415,1418,1425,1428,1431,1435,1437,1441,1443,1445,1447,1451,1459,1467,1479,1485,1494,1497,1508,1511,1512,1517,1518,1547,1616,1686,1687,1697,1706,1858,1860,1864,1867,1870,1873,1876,1879,1882,1885,1889,1892,1895,1898,1902,1905,1909,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1935,1937,1938,1939,1940,1941,1942,1943,1944,1946,1947,1949,1950,1952,1954,1955,1957,1958,1959,1960,1961,1962,1964,1965,1966,1967,1968,1985,1987,1989,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2005,2006,2007,2008,2009,2010,2012,2016,2020,2021,2022,2023,2024,2025,2029,2030,2031,2032,2034,2036,2038,2040,2042,2043,2044,2045,2047,2049,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2065,2066,2067,2068,2070,2072,2073,2075,2076,2078,2080,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2095,2096,2097,2098,2100,2101,2102,2103,2104,2106,2108,2110,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2613,2690,2978,3063,3196,3272,3348,3425,3503,3609,3715,3794,4123,4180,5040,5114,5189,5254,5320,5380,5441,5513,5586,5653,5721,5780,5839,5898,5957,6016,6070,6124,6177,6231,6285,6339,6784,6858,6937,7010,7155,7227,7299,7372,7429,7560,7634,7708,7834,7906,7979,8049,8120,8180,8241,8310,8379,8449,8523,8599,8663,8740,8816,8893,8958,9027,9104,9179,9248,9316,9393,9459,9520,9617,9682,9751,9850,9921,9980,10038,10095,10154,10218,10289,10361,10433,10505,10577,10644,10712,10780,10839,10902,10966,11056,11147,11207,11273,11340,11406,11476,11540,11593,11660,11721,11788,11901,11959,12022,12087,12152,12227,12300,12372,12421,12482,12543,12604,12666,12730,12794,12858,12923,12986,13046,13107,13173,13232,13292,13354,13425,13485,14184,14270,14520,14610,14697,14785,14867,14950,15040,16977,17029,17087,17132,17198,17262,17319,17376,19553,19610,19658,19707,19962,20332,20379,20637,21922,22268,22332,22454,22775,22849,22919,22997,23051,23121,23206,23254,23300,23361,23424,23490,23554,23625,23688,23753,23817,23878,23939,23991,24064,24138,24207,24282,24356,24430,24571,28212,28573,28651,28741,28829,28925,29015,29597,29686,29933,30214,30466,30751,31144,31621,31843,32065,32341,32568,32798,33028,33258,33488,33715,34134,34360,34785,35015,35443,35662,35945,36153,36284,36511,36937,37162,37589,37810,38235,38355,38631,38932,39256,39547,39861,39998,40129,40234,40476,40643,40847,41055,41326,41438,41550,41655,41772,41986,42132,42272,42358,42706,42794,43040,43458,43707,43789,43887,44479,44579,44831,45255,45510,45604,45693,45930,47954,48196,48298,48551,50707,61239,62755,73386,74914,76671,77297,77717,78778,80043,80299,80535,81082,81576,82181,82379,82959,83523,83898,84016,84554,84711,84907,85180,85436,85606,85747,85811,86176,86543,87219,87483,87821,88174,88268,88454,88760,89022,89147,89274,89513,89724,89843,90036,90213,90668,90849,90971,91230,91343,91530,91632,91739,91868,92143,92651,93147,94024,94318,94888,95037,95769,95941,96025,96361,96453,98519,103765,109154,109216,109794,110378,118325,118438,118667,118827,118979,119150,119316,119485,119652,119815,120058,120228,120401,120572,120846,121045,121250,121580,121664,121760,121856,121954,122054,122156,122258,122360,122462,122564,122664,122760,122872,123001,123124,123255,123386,123484,123598,123692,123832,123966,124062,124174,124274,124390,124486,124598,124698,124838,124974,125138,125268,125426,125576,125717,125861,125996,126108,126258,126386,126514,126650,126782,126912,127042,127154,128434,128580,128724,128862,128928,129018,129094,129198,129288,129390,129498,129606,129706,129786,129878,129976,130086,130164,130270,130362,130466,130576,130698,130861,131018,131098,131198,131288,131398,131488,131729,131823,131929,132021,132121,132233,132347,132463,132579,132673,132787,132899,133001,133121,133243,133325,133429,133549,133675,133773,133867,133955,134067,134183,134305,134417,134592,134708,134794,134886,134998,135122,135189,135315,135383,135511,135655,135783,135852,135947,136062,136175,136274,136383,136494,136605,136706,136811,136911,137041,137132,137255,137349,137461,137547,137651,137747,137835,137953,138057,138161,138287,138375,138483,138583,138673,138783,138867,138969,139053,139107,139171,139277,139363,139473,139557", "endLines": "4,27,28,59,60,61,62,64,65,66,67,69,70,74,75,77,78,79,80,81,82,83,84,89,90,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,129,130,131,132,134,135,136,137,138,140,141,142,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,237,238,242,243,244,245,246,247,248,278,279,280,281,282,283,284,285,321,322,323,324,329,337,338,343,367,374,375,377,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,446,451,452,453,454,455,463,464,468,472,476,481,487,494,498,502,507,511,515,519,523,527,531,537,541,547,551,557,561,566,570,573,577,583,587,593,597,603,606,610,614,618,622,626,627,628,629,632,635,638,641,645,646,647,648,649,652,654,656,658,663,664,668,674,678,679,681,692,693,697,703,707,708,709,713,740,744,745,749,777,947,973,1144,1170,1201,1209,1215,1229,1251,1256,1261,1271,1280,1289,1293,1300,1308,1315,1316,1325,1328,1331,1335,1339,1343,1346,1347,1352,1357,1367,1372,1379,1385,1386,1389,1393,1398,1400,1402,1405,1408,1410,1414,1417,1424,1427,1430,1434,1436,1440,1442,1444,1446,1450,1458,1466,1478,1484,1493,1496,1507,1510,1511,1516,1517,1522,1615,1685,1686,1696,1705,1706,1859,1863,1866,1869,1872,1875,1878,1881,1884,1888,1891,1894,1897,1901,1904,1908,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1934,1936,1937,1938,1939,1940,1941,1942,1943,1945,1946,1948,1949,1951,1953,1954,1956,1957,1958,1959,1960,1961,1963,1964,1965,1966,1967,1968,1986,1988,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2004,2005,2006,2007,2008,2009,2011,2015,2019,2020,2021,2022,2023,2024,2028,2029,2030,2031,2033,2035,2037,2039,2041,2042,2043,2044,2046,2048,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2064,2065,2066,2067,2069,2071,2072,2074,2075,2077,2079,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2094,2095,2096,2097,2099,2100,2101,2102,2103,2105,2107,2109,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2685,2763,3058,3140,3267,3343,3420,3498,3604,3710,3789,3869,4175,4233,5109,5184,5249,5315,5375,5436,5508,5581,5648,5716,5775,5834,5893,5952,6011,6065,6119,6172,6226,6280,6334,6388,6853,6932,7005,7079,7222,7294,7367,7424,7482,7629,7703,7778,7901,7974,8044,8115,8175,8236,8305,8374,8444,8518,8594,8658,8735,8811,8888,8953,9022,9099,9174,9243,9311,9388,9454,9515,9612,9677,9746,9845,9916,9975,10033,10090,10149,10213,10284,10356,10428,10500,10572,10639,10707,10775,10834,10897,10961,11051,11142,11202,11268,11335,11401,11471,11535,11588,11655,11716,11783,11896,11954,12017,12082,12147,12222,12295,12367,12416,12477,12538,12599,12661,12725,12789,12853,12918,12981,13041,13102,13168,13227,13287,13349,13420,13480,13548,14265,14352,14605,14692,14780,14862,14945,15035,15126,17024,17082,17127,17193,17257,17314,17371,17425,19605,19653,19702,19753,19991,20374,20423,20678,21949,22327,22389,22506,22844,22914,22992,23046,23116,23201,23249,23295,23356,23419,23485,23549,23620,23683,23748,23812,23873,23934,23986,24059,24133,24202,24277,24351,24425,24566,24636,28260,28646,28736,28824,28920,29010,29592,29681,29928,30209,30461,30746,31139,31616,31838,32060,32336,32563,32793,33023,33253,33483,33710,34129,34355,34780,35010,35438,35657,35940,36148,36279,36506,36932,37157,37584,37805,38230,38350,38626,38927,39251,39542,39856,39993,40124,40229,40471,40638,40842,41050,41321,41433,41545,41650,41767,41981,42127,42267,42353,42701,42789,43035,43453,43702,43784,43882,44474,44574,44826,45250,45505,45599,45688,45925,47949,48191,48293,48546,50702,61234,62750,73381,74909,76666,77292,77712,78773,80038,80294,80530,81077,81571,82176,82374,82954,83518,83893,84011,84549,84706,84902,85175,85431,85601,85742,85806,86171,86538,87214,87478,87816,88169,88263,88449,88755,89017,89142,89269,89508,89719,89838,90031,90208,90663,90844,90966,91225,91338,91525,91627,91734,91863,92138,92646,93142,94019,94313,94883,95032,95764,95936,96020,96356,96448,96726,103760,109149,109211,109789,110373,110464,118433,118662,118822,118974,119145,119311,119480,119647,119810,120053,120223,120396,120567,120841,121040,121245,121575,121659,121755,121851,121949,122049,122151,122253,122355,122457,122559,122659,122755,122867,122996,123119,123250,123381,123479,123593,123687,123827,123961,124057,124169,124269,124385,124481,124593,124693,124833,124969,125133,125263,125421,125571,125712,125856,125991,126103,126253,126381,126509,126645,126777,126907,127037,127149,127289,128575,128719,128857,128923,129013,129089,129193,129283,129385,129493,129601,129701,129781,129873,129971,130081,130159,130265,130357,130461,130571,130693,130856,131013,131093,131193,131283,131393,131483,131724,131818,131924,132016,132116,132228,132342,132458,132574,132668,132782,132894,132996,133116,133238,133320,133424,133544,133670,133768,133862,133950,134062,134178,134300,134412,134587,134703,134789,134881,134993,135117,135184,135310,135378,135506,135650,135778,135847,135942,136057,136170,136269,136378,136489,136600,136701,136806,136906,137036,137127,137250,137344,137456,137542,137646,137742,137830,137948,138052,138156,138282,138370,138478,138578,138668,138778,138862,138964,139048,139102,139166,139272,139358,139468,139552,139672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a5693044e33581e61900f855ad55c355\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "368", "startColumns": "4", "startOffsets": "21954", "endColumns": "42", "endOffsets": "21992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6ca6840a4c8abc49ec8115d4c444a97f\\transformed\\jetified-firebase-messaging-25.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "441", "startColumns": "4", "startOffsets": "27946", "endColumns": "81", "endOffsets": "28023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\473c0c0b56d00097ce6c8c24ae99c62d\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3,2129", "startColumns": "4,4", "startOffsets": "164,139816", "endLines": "3,2131", "endColumns": "60,12", "endOffsets": "220,139956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9defe5ddc203fb6a2ff99b4fc359331e\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "327,328,333,340,341,362,363,364,365,366", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19875,19915,20132,20470,20525,21656,21710,21762,21811,21872", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19910,19957,20170,20520,20567,21705,21757,21806,21867,21917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\25304d0d91eff6e53f7530bf0f9772e8\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "63,127,267,268,269,270,271,272,273,334,335,336,379,380,435,437,442,443,448,449,450,1523,1707,1710,1716,1722,1725,1731,1735,1738,1745,1751,1754,1760,1765,1770,1777,1779,1785,1791,1799,1804,1811,1816,1822,1826,1833,1837,1843,1849,1852,1856,1857", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6667,16332,16396,16451,16519,16586,16651,16708,20175,20223,20271,22579,22642,27568,27674,28028,28072,28336,28475,28525,96731,110469,110574,110819,111157,111303,111643,111855,112018,112425,112763,112886,113225,113464,113721,114092,114152,114490,114776,115225,115517,115905,116210,116554,116799,117129,117336,117604,117877,118021,118222,118269", "endLines": "63,127,267,268,269,270,271,272,273,334,335,336,379,380,435,437,442,445,448,449,450,1539,1709,1715,1721,1724,1730,1734,1737,1744,1750,1753,1759,1764,1769,1776,1778,1784,1790,1798,1803,1810,1815,1821,1825,1832,1836,1842,1848,1851,1855,1856,1857", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "2268,6731,16391,16446,16514,16581,16646,16703,16760,20218,20266,20327,22637,22700,27601,27726,28067,28207,28470,28520,28568,98164,110569,110814,111152,111298,111638,111850,112013,112420,112758,112881,113220,113459,113716,114087,114147,114485,114771,115220,115512,115900,116205,116549,116794,117124,117331,117599,117872,118016,118217,118264,118320"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1540,1544", "startColumns": "4,4", "startOffsets": "98169,98350", "endLines": "1543,1546", "endColumns": "12,12", "endOffsets": "98345,98514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f67accb42148273cb637a68c7df17dc6\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "22154", "endColumns": "49", "endOffsets": "22199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d22f1c95abad4bd2e955d74dd7590a3f\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "93,94,95,96,97,98,99,100,417,418,419,420,421,422,423,424,426,427,428,429,430,431,432,433,434", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4369,4459,4539,4629,4719,4799,4880,4960,25235,25340,25521,25646,25753,25933,26056,26172,26442,26630,26735,26916,27041,27216,27364,27427,27489", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "4454,4534,4624,4714,4794,4875,4955,5035,25335,25516,25641,25748,25928,26051,26167,26270,26625,26730,26911,27036,27211,27359,27422,27484,27563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a7355c573431e9b1d705c6f07d466102\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "29,71,72,91,92,123,125,230,231,232,233,234,235,236,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,331,332,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,381,410,411,412,413,414,415,416,447,1969,1970,1975,1978,1983,2127,2128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2768,2840,4238,4303,6393,6515,13696,13766,13834,13906,13976,14037,14111,15354,15415,15476,15538,15602,15664,15725,15793,15893,15953,16019,16092,16161,16218,16270,17430,17502,17578,17643,17702,17761,17821,17881,17941,18001,18061,18121,18181,18241,18301,18361,18420,18480,18540,18600,18660,18720,18780,18840,18900,18960,19020,19079,19139,19199,19258,19317,19376,19435,19494,20062,20097,20683,20738,20801,20856,20914,20970,21028,21089,21152,21209,21260,21318,21368,21429,21486,21552,21586,21621,22705,24724,24791,24863,24932,25001,25075,25147,28265,127294,127411,127678,127971,128238,139677,139749", "endLines": "29,71,72,91,92,123,125,230,231,232,233,234,235,236,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,331,332,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,381,410,411,412,413,414,415,416,447,1969,1973,1975,1981,1983,2127,2128", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "1040,2835,2923,4298,4364,6457,6573,13761,13829,13901,13971,14032,14106,14179,15410,15471,15533,15597,15659,15720,15788,15888,15948,16014,16087,16156,16213,16265,16327,17497,17573,17638,17697,17756,17816,17876,17936,17996,18056,18116,18176,18236,18296,18356,18415,18475,18535,18595,18655,18715,18775,18835,18895,18955,19015,19074,19134,19194,19253,19312,19371,19430,19489,19548,20092,20127,20733,20796,20851,20909,20965,21023,21084,21147,21204,21255,21313,21363,21424,21481,21547,21581,21616,21651,22770,24786,24858,24927,24996,25070,25142,25230,28331,127406,127607,127783,128167,128362,139744,139811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\832d1f825346ca2fdcec1dede0243727\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "409", "startColumns": "4", "startOffsets": "24641", "endColumns": "82", "endOffsets": "24719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e049e7f9ae1205a5f948ad90564674a9\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19758", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,325", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a7734484d3c0de25203417a094b6816f\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "239,240,241,249,250,251,330", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14357,14416,14464,15131,15206,15282,19996", "endColumns": "58,47,55,74,75,71,65", "endOffsets": "14411,14459,14515,15201,15277,15349,20057"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "5,12,11,4,3,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "188,409,358,135,87,271", "endColumns": "46,49,50,52,47,50", "endOffsets": "230,454,404,183,130,317"}, "to": {"startLines": "68,73,76,124,128,143", "startColumns": "4,4,4,4,4,4", "startOffsets": "2566,2928,3145,6462,6736,7783", "endColumns": "46,49,50,52,47,50", "endOffsets": "2608,2973,3191,6510,6779,7829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fcc999463ce5662267ea14174d4f23fb\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "21997", "endColumns": "42", "endOffsets": "22035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\752f4acbe9971a83fce3d5c291b8494e\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "339,370", "startColumns": "4,4", "startOffsets": "20428,22040", "endColumns": "41,59", "endOffsets": "20465,22095"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-mergeDebugResources-50:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a2bb78523596e649afe63bece9c973ac\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "122,128,134,268,269,270,271,370,1968,1970,1971,1976,1978", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6377,6835,7238,16465,16518,16571,16624,22094,127312,127488,127610,127872,128067", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6461,6901,7306,16513,16566,16619,16672,22149,127373,127605,127666,127933,128129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d4d69cd4063662f4870b4bb3bcda6c2b\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "365", "startColumns": "4", "startOffsets": "21800", "endColumns": "53", "endOffsets": "21849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df104370204d6f73bddc8d757655b422\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "320,336,367,3021,3026", "startColumns": "4,4,4,4,4", "startOffsets": "19518,20272,21904,171959,172129", "endLines": "320,336,367,3025,3029", "endColumns": "56,64,63,24,24", "endOffsets": "19570,20332,21963,172124,172273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\744a14c4162cc1cc008e947e42d41a58\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "372,419", "startColumns": "4,4", "startOffsets": "22211,25975", "endColumns": "67,166", "endOffsets": "22274,26137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3d0dabce7e4774ae1b88c68846e72921\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,222,223,430,432,433,434", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13253,13324,27306,27431,27498,27577", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13319,13391,27369,27493,27572,27641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\210bb542128be41e9375e7e70847760f\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,231,232,236,237,238,239,240,241,242,272,273,274,275,276,277,278,279,315,316,317,318,323,331,332,337,361,368,369,371,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,440,445,446,447,448,449,450,458,459,463,467,471,476,482,489,493,497,502,506,510,514,518,522,526,532,536,542,546,552,556,561,565,568,572,578,582,588,592,598,601,605,609,613,617,621,622,623,624,627,630,633,636,640,641,642,643,644,647,649,651,653,658,659,663,669,673,674,676,687,688,692,698,702,703,704,708,735,739,740,744,772,942,968,1139,1165,1196,1204,1210,1224,1246,1251,1256,1266,1275,1284,1288,1295,1303,1310,1311,1320,1323,1326,1330,1334,1338,1341,1342,1347,1352,1362,1367,1374,1380,1381,1384,1388,1393,1395,1397,1400,1403,1405,1409,1412,1419,1422,1425,1429,1431,1435,1437,1439,1441,1445,1453,1461,1473,1479,1488,1491,1502,1505,1506,1511,1512,1541,1610,1680,1681,1691,1700,1852,1854,1858,1861,1864,1867,1870,1873,1876,1879,1883,1886,1889,1892,1896,1899,1903,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1929,1931,1932,1933,1934,1935,1936,1937,1938,1940,1941,1943,1944,1946,1948,1949,1951,1952,1953,1954,1955,1956,1958,1959,1960,1961,1962,1979,1981,1983,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1999,2000,2001,2002,2003,2004,2006,2010,2014,2015,2016,2017,2018,2019,2023,2024,2025,2026,2028,2030,2032,2034,2036,2037,2038,2039,2041,2043,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2059,2060,2061,2062,2064,2066,2067,2069,2070,2072,2074,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2089,2090,2091,2092,2094,2095,2096,2097,2098,2100,2102,2104,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2126,2201,2204,2207,2210,2224,2241,2283,2312,2339,2348,2410,2774,2805,2943,3069,3093,3099,3128,3149,3273,3301,3307,3451,3477,3544,3615,3715,3735,3790,3802,3828", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5573,5632,5691,5750,5809,5868,5922,5976,6029,6083,6137,6191,6535,6609,6688,6761,6906,6978,7050,7123,7180,7311,7385,7459,7534,7606,7679,7749,7820,7880,7941,8010,8079,8149,8223,8299,8363,8440,8516,8593,8658,8727,8804,8879,8948,9016,9093,9159,9220,9317,9382,9451,9550,9621,9680,9738,9795,9854,9918,9989,10061,10133,10205,10277,10344,10412,10480,10539,10602,10666,10756,10847,10907,10973,11040,11106,11176,11240,11293,11360,11421,11488,11601,11659,11722,11787,11852,11927,12000,12072,12121,12182,12243,12304,12366,12430,12494,12558,12623,12686,12746,12807,12873,12932,12992,13054,13125,13185,13884,13970,14220,14310,14397,14485,14567,14650,14740,16677,16729,16787,16832,16898,16962,17019,17076,19253,19310,19358,19407,19662,20032,20079,20337,21622,21968,22032,22154,22475,22549,22619,22697,22751,22821,22906,22954,23000,23061,23124,23190,23254,23325,23388,23453,23517,23578,23639,23691,23764,23838,23907,23982,24056,24130,24271,27912,28273,28351,28441,28529,28625,28715,29297,29386,29633,29914,30166,30451,30844,31321,31543,31765,32041,32268,32498,32728,32958,33188,33415,33834,34060,34485,34715,35143,35362,35645,35853,35984,36211,36637,36862,37289,37510,37935,38055,38331,38632,38956,39247,39561,39698,39829,39934,40176,40343,40547,40755,41026,41138,41250,41355,41472,41686,41832,41972,42058,42406,42494,42740,43158,43407,43489,43587,44179,44279,44531,44955,45210,45304,45393,45630,47654,47896,47998,48251,50407,60939,62455,73086,74614,76371,76997,77417,78478,79743,79999,80235,80782,81276,81881,82079,82659,83223,83598,83716,84254,84411,84607,84880,85136,85306,85447,85511,85876,86243,86919,87183,87521,87874,87968,88154,88460,88722,88847,88974,89213,89424,89543,89736,89913,90368,90549,90671,90930,91043,91230,91332,91439,91568,91843,92351,92847,93724,94018,94588,94737,95469,95641,95725,96061,96153,98219,103465,108854,108916,109494,110078,118025,118138,118367,118527,118679,118850,119016,119185,119352,119515,119758,119928,120101,120272,120546,120745,120950,121280,121364,121460,121556,121654,121754,121856,121958,122060,122162,122264,122364,122460,122572,122701,122824,122955,123086,123184,123298,123392,123532,123666,123762,123874,123974,124090,124186,124298,124398,124538,124674,124838,124968,125126,125276,125417,125561,125696,125808,125958,126086,126214,126350,126482,126612,126742,126854,128134,128280,128424,128562,128628,128718,128794,128898,128988,129090,129198,129306,129406,129486,129578,129676,129786,129864,129970,130062,130166,130276,130398,130561,130718,130798,130898,130988,131098,131188,131429,131523,131629,131721,131821,131933,132047,132163,132279,132373,132487,132599,132701,132821,132943,133025,133129,133249,133375,133473,133567,133655,133767,133883,134005,134117,134292,134408,134494,134586,134698,134822,134889,135015,135083,135211,135355,135483,135552,135647,135762,135875,135974,136083,136194,136305,136406,136511,136611,136741,136832,136955,137049,137161,137247,137351,137447,137535,137653,137757,137861,137987,138075,138183,138283,138373,138483,138567,138669,138753,138807,138871,138977,139063,139173,139257,139661,142277,142395,142510,142590,142951,143537,144941,146285,147646,148034,150809,160898,161938,168751,173127,173878,174140,174987,175366,179644,180498,180727,185335,186345,188297,190697,194821,195565,197696,198036,199347", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,231,232,236,237,238,239,240,241,242,272,273,274,275,276,277,278,279,315,316,317,318,323,331,332,337,361,368,369,371,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,440,445,446,447,448,449,457,458,462,466,470,475,481,488,492,496,501,505,509,513,517,521,525,531,535,541,545,551,555,560,564,567,571,577,581,587,591,597,600,604,608,612,616,620,621,622,623,626,629,632,635,639,640,641,642,643,646,648,650,652,657,658,662,668,672,673,675,686,687,691,697,701,702,703,707,734,738,739,743,771,941,967,1138,1164,1195,1203,1209,1223,1245,1250,1255,1265,1274,1283,1287,1294,1302,1309,1310,1319,1322,1325,1329,1333,1337,1340,1341,1346,1351,1361,1366,1373,1379,1380,1383,1387,1392,1394,1396,1399,1402,1404,1408,1411,1418,1421,1424,1428,1430,1434,1436,1438,1440,1444,1452,1460,1472,1478,1487,1490,1501,1504,1505,1510,1511,1516,1609,1679,1680,1690,1699,1700,1853,1857,1860,1863,1866,1869,1872,1875,1878,1882,1885,1888,1891,1895,1898,1902,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1928,1930,1931,1932,1933,1934,1935,1936,1937,1939,1940,1942,1943,1945,1947,1948,1950,1951,1952,1953,1954,1955,1957,1958,1959,1960,1961,1962,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1998,1999,2000,2001,2002,2003,2005,2009,2013,2014,2015,2016,2017,2018,2022,2023,2024,2025,2027,2029,2031,2033,2035,2036,2037,2038,2040,2042,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2058,2059,2060,2061,2063,2065,2066,2068,2069,2071,2073,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2088,2089,2090,2091,2093,2094,2095,2096,2097,2099,2101,2103,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2200,2203,2206,2209,2223,2229,2250,2311,2338,2347,2409,2768,2777,2832,2960,3092,3098,3104,3148,3272,3292,3306,3310,3456,3511,3555,3680,3734,3789,3801,3827,3834", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5627,5686,5745,5804,5863,5917,5971,6024,6078,6132,6186,6240,6604,6683,6756,6830,6973,7045,7118,7175,7233,7380,7454,7529,7601,7674,7744,7815,7875,7936,8005,8074,8144,8218,8294,8358,8435,8511,8588,8653,8722,8799,8874,8943,9011,9088,9154,9215,9312,9377,9446,9545,9616,9675,9733,9790,9849,9913,9984,10056,10128,10200,10272,10339,10407,10475,10534,10597,10661,10751,10842,10902,10968,11035,11101,11171,11235,11288,11355,11416,11483,11596,11654,11717,11782,11847,11922,11995,12067,12116,12177,12238,12299,12361,12425,12489,12553,12618,12681,12741,12802,12868,12927,12987,13049,13120,13180,13248,13965,14052,14305,14392,14480,14562,14645,14735,14826,16724,16782,16827,16893,16957,17014,17071,17125,19305,19353,19402,19453,19691,20074,20123,20378,21649,22027,22089,22206,22544,22614,22692,22746,22816,22901,22949,22995,23056,23119,23185,23249,23320,23383,23448,23512,23573,23634,23686,23759,23833,23902,23977,24051,24125,24266,24336,27960,28346,28436,28524,28620,28710,29292,29381,29628,29909,30161,30446,30839,31316,31538,31760,32036,32263,32493,32723,32953,33183,33410,33829,34055,34480,34710,35138,35357,35640,35848,35979,36206,36632,36857,37284,37505,37930,38050,38326,38627,38951,39242,39556,39693,39824,39929,40171,40338,40542,40750,41021,41133,41245,41350,41467,41681,41827,41967,42053,42401,42489,42735,43153,43402,43484,43582,44174,44274,44526,44950,45205,45299,45388,45625,47649,47891,47993,48246,50402,60934,62450,73081,74609,76366,76992,77412,78473,79738,79994,80230,80777,81271,81876,82074,82654,83218,83593,83711,84249,84406,84602,84875,85131,85301,85442,85506,85871,86238,86914,87178,87516,87869,87963,88149,88455,88717,88842,88969,89208,89419,89538,89731,89908,90363,90544,90666,90925,91038,91225,91327,91434,91563,91838,92346,92842,93719,94013,94583,94732,95464,95636,95720,96056,96148,96426,103460,108849,108911,109489,110073,110164,118133,118362,118522,118674,118845,119011,119180,119347,119510,119753,119923,120096,120267,120541,120740,120945,121275,121359,121455,121551,121649,121749,121851,121953,122055,122157,122259,122359,122455,122567,122696,122819,122950,123081,123179,123293,123387,123527,123661,123757,123869,123969,124085,124181,124293,124393,124533,124669,124833,124963,125121,125271,125412,125556,125691,125803,125953,126081,126209,126345,126477,126607,126737,126849,126989,128275,128419,128557,128623,128713,128789,128893,128983,129085,129193,129301,129401,129481,129573,129671,129781,129859,129965,130057,130161,130271,130393,130556,130713,130793,130893,130983,131093,131183,131424,131518,131624,131716,131816,131928,132042,132158,132274,132368,132482,132594,132696,132816,132938,133020,133124,133244,133370,133468,133562,133650,133762,133878,134000,134112,134287,134403,134489,134581,134693,134817,134884,135010,135078,135206,135350,135478,135547,135642,135757,135870,135969,136078,136189,136300,136401,136506,136606,136736,136827,136950,137044,137156,137242,137346,137442,137530,137648,137752,137856,137982,138070,138178,138278,138368,138478,138562,138664,138748,138802,138866,138972,139058,139168,139252,139372,142272,142390,142505,142585,142946,143179,144049,146280,147641,148029,150804,160708,161028,163290,169318,173873,174135,174335,175361,179639,180245,180722,180873,185545,187423,188604,193718,195560,197691,198031,199342,199545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a5693044e33581e61900f855ad55c355\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "362", "startColumns": "4", "startOffsets": "21654", "endColumns": "42", "endOffsets": "21692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6ca6840a4c8abc49ec8115d4c444a97f\\transformed\\jetified-firebase-messaging-25.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "435", "startColumns": "4", "startOffsets": "27646", "endColumns": "81", "endOffsets": "27723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\473c0c0b56d00097ce6c8c24ae99c62d\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2123,2833,2839", "startColumns": "4,4,4,4", "startOffsets": "164,139516,163295,163506", "endLines": "3,2125,2838,2922", "endColumns": "60,12,24,24", "endOffsets": "220,139656,163501,168017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9defe5ddc203fb6a2ff99b4fc359331e\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "321,322,327,334,335,356,357,358,359,360", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19575,19615,19832,20170,20225,21356,21410,21462,21511,21572", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19610,19657,19870,20220,20267,21405,21457,21506,21567,21617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\25304d0d91eff6e53f7530bf0f9772e8\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,123,261,262,263,264,265,266,267,328,329,330,373,374,429,431,436,437,442,443,444,1517,1701,1704,1710,1716,1719,1725,1729,1732,1739,1745,1748,1754,1759,1764,1771,1773,1779,1785,1793,1798,1805,1810,1816,1820,1827,1831,1837,1843,1846,1850,1851,2769,2784,2923,2961,3105,3293,3311,3375,3385,3395,3402,3408,3512,3681,3698", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6466,16032,16096,16151,16219,16286,16351,16408,19875,19923,19971,22279,22342,27268,27374,27728,27772,28036,28175,28225,96431,110169,110274,110519,110857,111003,111343,111555,111718,112125,112463,112586,112925,113164,113421,113792,113852,114190,114476,114925,115217,115605,115910,116254,116499,116829,117036,117304,117577,117721,117922,117969,160713,161236,168022,169323,174340,180250,180878,182803,183085,183390,183652,183912,187428,193723,194253", "endLines": "63,123,261,262,263,264,265,266,267,328,329,330,373,374,429,431,436,439,442,443,444,1533,1703,1709,1715,1718,1724,1728,1731,1738,1744,1747,1753,1758,1763,1770,1772,1778,1784,1792,1797,1804,1809,1815,1819,1826,1830,1836,1842,1845,1849,1850,1851,2773,2794,2942,2964,3114,3300,3374,3384,3394,3401,3407,3450,3524,3697,3714", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6530,16091,16146,16214,16281,16346,16403,16460,19918,19966,20027,22337,22400,27301,27426,27767,27907,28170,28220,28268,97864,110269,110514,110852,110998,111338,111550,111713,112120,112458,112581,112920,113159,113416,113787,113847,114185,114471,114920,115212,115600,115905,116249,116494,116824,117031,117299,117572,117716,117917,117964,118020,160893,161632,168746,169467,174667,180493,182798,183080,183385,183647,183907,185330,187875,194248,194816"}}, {"source": "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti-customer\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1534,1538", "startColumns": "4,4", "startOffsets": "97869,98050", "endLines": "1537,1540", "endColumns": "12,12", "endOffsets": "98045,98214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f67accb42148273cb637a68c7df17dc6\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "366", "startColumns": "4", "startOffsets": "21854", "endColumns": "49", "endOffsets": "21899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d22f1c95abad4bd2e955d74dd7590a3f\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,411,412,413,414,415,416,417,418,420,421,422,423,424,425,426,427,428,3115,3525", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,24935,25040,25221,25346,25453,25633,25756,25872,26142,26330,26435,26616,26741,26916,27064,27127,27189,174672,187880", "endLines": "90,91,92,93,94,95,96,97,411,412,413,414,415,416,417,418,420,421,422,423,424,425,426,427,428,3127,3543", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,25035,25216,25341,25448,25628,25751,25867,25970,26325,26430,26611,26736,26911,27059,27122,27184,27263,174982,188292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a7355c573431e9b1d705c6f07d466102\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "29,70,71,88,89,120,121,224,225,226,227,228,229,230,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,325,326,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,375,404,405,406,407,408,409,410,441,1963,1964,1969,1972,1977,2121,2122,2778,2795,2965,3000,3030,3063", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6245,6314,13396,13466,13534,13606,13676,13737,13811,15054,15115,15176,15238,15302,15364,15425,15493,15593,15653,15719,15792,15861,15918,15970,17130,17202,17278,17343,17402,17461,17521,17581,17641,17701,17761,17821,17881,17941,18001,18061,18120,18180,18240,18300,18360,18420,18480,18540,18600,18660,18720,18779,18839,18899,18958,19017,19076,19135,19194,19762,19797,20383,20438,20501,20556,20614,20670,20728,20789,20852,20909,20960,21018,21068,21129,21186,21252,21286,21321,22405,24424,24491,24563,24632,24701,24775,24847,27965,126994,127111,127378,127671,127938,139377,139449,161033,161637,169472,171278,172278,172960", "endLines": "29,70,71,88,89,120,121,224,225,226,227,228,229,230,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,325,326,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,375,404,405,406,407,408,409,410,441,1963,1967,1969,1975,1977,2121,2122,2783,2804,2999,3020,3062,3068", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,6309,6372,13461,13529,13601,13671,13732,13806,13879,15110,15171,15233,15297,15359,15420,15488,15588,15648,15714,15787,15856,15913,15965,16027,17197,17273,17338,17397,17456,17516,17576,17636,17696,17756,17816,17876,17936,17996,18056,18115,18175,18235,18295,18355,18415,18475,18535,18595,18655,18715,18774,18834,18894,18953,19012,19071,19130,19189,19248,19792,19827,20433,20496,20551,20609,20665,20723,20784,20847,20904,20955,21013,21063,21124,21181,21247,21281,21316,21351,22470,24486,24558,24627,24696,24770,24842,24930,28031,127106,127307,127483,127867,128062,139444,139511,161231,161933,171273,171954,172955,173122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\832d1f825346ca2fdcec1dede0243727\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "403", "startColumns": "4", "startOffsets": "24341", "endColumns": "82", "endOffsets": "24419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e049e7f9ae1205a5f948ad90564674a9\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,319,2230,2236,3556,3564,3579", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19458,143184,143379,188609,188891,189505", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,319,2235,2240,3563,3578,3594", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19513,143374,143532,188886,189500,190154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a7734484d3c0de25203417a094b6816f\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "233,234,235,243,244,245,324,3457", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14057,14116,14164,14831,14906,14982,19696,185550", "endLines": "233,234,235,243,244,245,324,3476", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14111,14159,14215,14901,14977,15049,19757,186340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fcc999463ce5662267ea14174d4f23fb\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21697", "endColumns": "42", "endOffsets": "21735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\752f4acbe9971a83fce3d5c291b8494e\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "333,364", "startColumns": "4,4", "startOffsets": "20128,21740", "endColumns": "41,59", "endOffsets": "20165,21795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\60936a00099a90688457e5b35c570155\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2251,2267,2273,3595,3611", "startColumns": "4,4,4,4,4", "startOffsets": "144054,144479,144657,190159,190570", "endLines": "2266,2272,2282,3610,3614", "endColumns": "24,24,24,24,24", "endOffsets": "144474,144652,144936,190565,190692"}}]}]}