[{"merged": "org.adscloud.dalti.dalti_customer.app-debug-52:/xml_file_paths.xml.flat", "source": "org.adscloud.dalti.dalti_customer.app-main-47:/xml/file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-debug-52:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-main-47:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-debug-52:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-main-47:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-debug-52:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-main-47:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-debug-52:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-main-47:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "org.adscloud.dalti.dalti_customer.app-debug-52:/xml_network_security_config.xml.flat", "source": "org.adscloud.dalti.dalti_customer.app-main-47:/xml/network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-debug-52:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-main-47:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-debug-52:\\drawable-v21_launch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\org.adscloud.dalti.dalti_customer.app-main-47:\\drawable-v21\\launch_background.xml"}]